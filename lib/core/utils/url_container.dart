
class UrlContainer{

  static const String baseUrl = 'https://script.viserlab.com/signallab/demo/';
  // static const String baseUrl = 'https://members.signalking.io/';

  static const String loginEndPoint          = 'api/login';
  static const String referralEndPoint       = "api/referrals";
  static const String registrationEndPoint   = 'api/register';
  static const String userDashboardEndPoint  = 'api/dashboard';
  static const String forgetPasswordEndPoint = 'api/password/email';
  static const String passwordVerifyEndPoint = 'api/password/verify-code';
  static const String resetPasswordEndPoint  = 'api/password/reset';
  static const String countryEndPoint        = 'api/get-countries';
  static const String socialLoginEndPoint    = 'api/social-login';
  static const String languageUrl            = 'api/language/';

  
  static const String accountDisable = "api/delete-account";

    // pricing plan
  static const String packagesEndPoint        = "api/packages";
  static const String purchasePackageEndPoint = "api/purchase/package";
  static const String renewPackageEndPoint    = "api/renew/package";

    // signals
  static const String signalsEndPoint = "api/signals";

  static const String verifyEmailEndPoint      = 'api/verify-email';
  static const String verifySmsEndPoint        = 'api/verify-mobile';
  static const String verify2FAUrl             = 'api/verify-g2fa';
  static const String resendVerifyCodeEndPoint = 'api/resend-verify/';


  static const String depositHistoryEndPoint = 'api/deposit/history';
  static const String depositMethodEndPoint  = 'api/deposit/methods';
  static const String depositInsertEndPoint  = 'api/deposit/insert';

  static const String authorizationCodeEndPoint = 'api/authorization';
  static const String generalSettingEndPoint    = 'api/general-setting';
  static const String privacyPolicyEndPoint     = 'api/policies';

  static const String getProfileEndPoint      = 'api/user-info';
  static const String updateProfileEndPoint   = 'api/profile-setting';
  static const String profileCompleteEndPoint = 'api/user-data-submit';
  static const String changePasswordEndPoint  = 'api/change-password';

    // transaction
  static const String transactionEndpoint = 'api/transactions';
  static const String deviceTokenEndPoint = 'api/add-device-token';
  static const String logout              = 'api/logout';


  
  static const String twoFactor = "api/twofactor";
  static const String twoFactorEnable = "api/twofactor/enable";
  static const String twoFactorDisable = "api/twofactor/disable";
  static const String faqEndPoint = "api/faq";

    static const String communityGroupsEndPoint = 'community-groups';

  static const String supportMethodsEndPoint = 'api/support/method';
  static const String supportListEndPoint = 'api/ticket';
  static const String storeSupportEndPoint = 'api/ticket/create';
  static const String supportViewEndPoint = 'api/ticket/view';
  static const String supportReplyEndPoint = 'api/ticket/reply';
  static const String supportCloseEndPoint = 'api/ticket/close';
  static const String supportDownloadEndPoint = 'api/ticket/download';
  static const String supportImagePath = 'https://url8.viserlab.com/signallab/assets/support/';
}
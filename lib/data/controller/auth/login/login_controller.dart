import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:signal_lab/core/utils/my_strings.dart';
import 'package:signal_lab/data/model/auth/login/login_response_model.dart';
import 'package:signal_lab/data/model/general_setting/general_setting_response_model.dart';
import 'package:signal_lab/data/model/global/response_model/response_model.dart';
import 'package:signal_lab/data/repo/auth/login/login_repo.dart';
import 'package:signal_lab/views/components/signin_with_linkdin/signin_with_linkedin.dart';
import 'package:signal_lab/views/components/snackbar/show_custom_snackbar.dart';
import '../../../../../core/helper/shared_pref_helper.dart';
import '../../../../../core/route/route.dart';
import '../../../model/user/user.dart';
import 'package:google_sign_in/google_sign_in.dart';


class LoginController extends GetxController {
  LoginRepo loginRepo;
  LoginController({required this.loginRepo});

  final FocusNode emailFocusNode = FocusNode();
  final FocusNode passwordFocusNode = FocusNode();

  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();

  String? email;
  String? password;

  List<String> errors = [];
  bool remember = false;

  void forgetPassword() {
    Get.toNamed(RouteHelper.forgotPasswordScreen);
  }
  

  void checkAndGotoNextStep(LoginResponseModel responseModel) async {
    print('🔄 Starting routing logic...');
    User? user = responseModel.data?.user;

    bool needEmailVerification = user?.ev == "1" ? false : true;
    bool needSmsVerification = user?.sv == '1' ? false : true;
    bool isProfileCompleteEnable = user?.profileComplete == '0' ? true : false;

    print('📋 User verification status:');
    print('   📧 Email verified (ev): ${user?.ev} -> needEmailVerification: $needEmailVerification');
    print('   📱 SMS verified (sv): ${user?.sv} -> needSmsVerification: $needSmsVerification');
    print('   ✏️ Profile complete: ${user?.profileComplete} -> isProfileCompleteEnable: $isProfileCompleteEnable');

    if (remember) {
      await loginRepo.apiClient.sharedPreferences.setBool(SharedPreferenceHelper.rememberMeKey, true);
    } else {
      await loginRepo.apiClient.sharedPreferences.setBool(SharedPreferenceHelper.rememberMeKey, false);
    }

    // Save user data to SharedPreferences
    String userId = user?.id.toString() ?? '-1';
    String accessToken = responseModel.data?.accessToken ?? '';
    String tokenType = responseModel.data?.tokenType ?? '';
    String userEmail = user?.email ?? '';
    String userPhone = user?.mobile ?? '';
    String userName = user?.username ?? '';

    print('💾 Saving user data to SharedPreferences...');
    print('   👤 User ID: $userId');
    print('   🔑 Access Token: ${accessToken.substring(0, 20)}...');
    print('   📧 Email: $userEmail');
    print('   👤 Username: $userName');

    await loginRepo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.userIdKey, userId);
    await loginRepo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.accessTokenKey, accessToken);
    await loginRepo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.accessTokenType, tokenType);
    await loginRepo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.userEmailKey, userEmail);
    await loginRepo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.userPhoneNumberKey, userPhone);
    await loginRepo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.userNameKey, userName);

    // Reinitialize the API client token after saving
    loginRepo.apiClient.initToken();
    print('🔄 API client token reinitialized');

    await loginRepo.sendUserToken();
    print('📱 Device token sent to server');

    // Step 4: Routing - Determine which screen to navigate to
    print('🧭 Determining navigation route...');
    if (isProfileCompleteEnable) {
      print('➡️ Navigating to Profile Complete Screen');
      Get.offAndToNamed(RouteHelper.profileCompleteScreen);
    } else if (needEmailVerification) {
      print('➡️ Navigating to Email Verification Screen');
      Get.offAndToNamed(RouteHelper.emailVerificationScreen);
    } else if (needSmsVerification) {
      print('➡️ Navigating to SMS Verification Screen');
      Get.offAndToNamed(RouteHelper.smsVerificationScreen);
    } else {
      print('➡️ Navigating to Home Screen');
      Get.offAndToNamed(RouteHelper.homeScreen);
    }
    print('✅ Navigation completed!');
  }

  bool isSubmitLoading = false;
  void loginUser() async {
    isSubmitLoading = true;
    update();

    ResponseModel model = await loginRepo.loginUser(emailController.text.toString(), passwordController.text.toString());

    if (model.statusCode == 200) {
      LoginResponseModel loginModel = LoginResponseModel.fromJson(jsonDecode(model.responseJson));

      if (loginModel.status.toString().toLowerCase() == MyStrings.success.toLowerCase()) {
        checkAndGotoNextStep(loginModel);
        if (remember) {
          changeRememberMe();
        }
      } else {
        MySnackbar.error(errorList: loginModel.message?.error ?? [MyStrings.loginFailedTryAgain]);
      }
    } else {
      MySnackbar.error(errorList: [model.message]);
    }

    isSubmitLoading = false;
    update();
  }

  void changeRememberMe() {
    remember = !remember;
    update();
  }

  void clearTextField() {
    passwordController.text = '';
    emailController.text = '';

    if (remember) {
      remember = false;
    }
    update();
  }

  final _googleSignIn = GoogleSignIn.instance;
  bool _isGoogleSignInInitialized = false;
  bool isGoogleSignInLoading = false;

  Future<void> _initializeGoogleSignIn() async {
    try {
      await _googleSignIn.initialize();
      _isGoogleSignInInitialized = true;
      debugPrint('✅ Google Sign In initialized successfully');
    } catch (e) {
      debugPrint('❌ Failed to initialize Google Sign-In: $e');
    }
  }

  Future<void> _ensureGoogleSignInInitialized() async {
    if (!_isGoogleSignInInitialized) {
      await _initializeGoogleSignIn();
    }
  }

  Future<void> signInWithGoogle() async {
    try {
      print('🚀 Starting Google Sign In...');
      isGoogleSignInLoading = true;
      update();

      await _ensureGoogleSignInInitialized();

      try {
        print('🔐 Authenticating with Google...');
        final GoogleSignInAccount account = await _googleSignIn.authenticate(
          scopeHint: ['email'],
        );
        print('👤 Google account: ${account.email}');

        final authClient = account.authorizationClient;
        final authorization = await authClient.authorizeScopes(['email']);

        print('✅ Google Sign In successful!');
        print('🔑 Access Token: ${authorization.accessToken.substring(0, 20)}...');

        // Call social login with the access token
        await socialLoginUser(provider: 'google', accessToken: authorization.accessToken);

      } on GoogleSignInException catch (e) {
        print('❌ Google Sign In error: ${e.code.name} - ${e.description}');
        if (e.code == GoogleSignInExceptionCode.canceled) {
          MySnackbar.error(errorList: ['Google Sign In was canceled']);
        } else {
          MySnackbar.error(errorList: ['Google Sign In failed: ${e.description ?? 'Unknown error'}']);
        }
      }
    } catch (e) {
      print('💥 Google Sign In exception: $e');
      MySnackbar.error(errorList: ['Google Sign In failed: ${e.toString()}']);
    }

    isGoogleSignInLoading = false;
    update();
  }

  //SIGN IN With LinkeDin
  bool isLinkedinLoading = false;
  Future<void> signInWithLinkedin(BuildContext context) async {
    try {
      isLinkedinLoading = false;
      update();

      SocialiteCredentials linkedinCredential = loginRepo.apiClient.getSocialCredentialsConfigData();
      String linkedinCredentialRedirectUrl = "${loginRepo.apiClient.getSocialCredentialsRedirectUrl()}/linkedin";
      SignInWithLinkedIn.signIn(
        context,
        config: LinkedInConfig(clientId: linkedinCredential.linkedin?.clientId ?? '', clientSecret: linkedinCredential.linkedin?.clientSecret ?? '', scope: ['openid', 'profile', 'email'], redirectUrl: linkedinCredentialRedirectUrl),
        onGetAuthToken: (data) {
          // Auth token received
        },
        onGetUserProfile: (token, user) async {
          await socialLoginUser(provider: 'linkedin', accessToken: token.accessToken ?? '');
        },
        onSignInError: (error) {
          MySnackbar.error(errorList: [error.description ?? MyStrings.loginFailedTryAgain.tr]);
          isLinkedinLoading = false;
          update();
        },
      );
    } catch (e) {
      debugPrint(e.toString());

      MySnackbar.error(errorList: [e.toString()]);
    }
  }

  Future socialLoginUser({
    String accessToken = '',
    String? provider,
  }) async {
    try {
      print('🔐 Starting social login with provider: $provider');
      print('🔑 Access token: ${accessToken.substring(0, 20)}...');

      // Step 2: API Call to /api/social-login
      print('📡 Making API call to /api/social-login...');
      ResponseModel responseModel = await loginRepo.socialLoginUser(
        accessToken: accessToken,
        provider: provider,
      );

      print('📡 API Response Status Code: ${responseModel.statusCode}');
      print('📡 API Response: ${responseModel.responseJson}');

      if (responseModel.statusCode == 200) {
        // Step 3: User Authentication - Parse backend response
        LoginResponseModel loginModel = LoginResponseModel.fromJson(jsonDecode(responseModel.responseJson));
        print('✅ Login model status: ${loginModel.status}');

        if (loginModel.status.toString().toLowerCase() == MyStrings.success.toLowerCase()) {
          remember = true;
          update();

          print('🚀 Social login successful! User authenticated.');
          print('👤 User: ${loginModel.data?.user?.username}');
          print('📧 Email: ${loginModel.data?.user?.email}');
          print('📧 Email verified: ${loginModel.data?.user?.ev}');
          print('📱 SMS verified: ${loginModel.data?.user?.sv}');
          print('✏️ Profile complete: ${loginModel.data?.user?.profileComplete}');

          // Step 4: Routing - Redirect to appropriate screen
          print('🔄 Proceeding to routing logic...');
          checkAndGotoNextStep(loginModel);
        } else {
          print('❌ Social login failed: ${loginModel.message?.error}');
          MySnackbar.error(errorList: loginModel.message?.error ?? [MyStrings.loginFailedTryAgain.tr]);
        }
      } else {
        print('❌ API Error: Status ${responseModel.statusCode} - ${responseModel.message}');
        MySnackbar.error(errorList: [responseModel.message]);
      }
    } catch (e) {
      print('💥 Social login exception: $e');
      MySnackbar.error(errorList: ['Social login failed: ${e.toString()}']);
    }
  }

  bool checkSocialAuthActiveOrNot({String provider = 'all'}) {
    if (provider == 'google') {
      return loginRepo.apiClient.getSocialCredentialsConfigData().google?.status == '1';
    } else if (provider == 'facebook') {
      return loginRepo.apiClient.getSocialCredentialsConfigData().facebook?.status == '1';
    } else if (provider == 'linkedin') {
      return loginRepo.apiClient.getSocialCredentialsConfigData().linkedin?.status == '1';
    } else {
      return loginRepo.apiClient.isSocialAnyOfSocialLoginOptionEnable();
    }
  }

}

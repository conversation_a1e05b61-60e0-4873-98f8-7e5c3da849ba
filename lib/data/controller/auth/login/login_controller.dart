import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:signal_lab/core/utils/my_strings.dart';
import 'package:signal_lab/data/model/auth/login/login_response_model.dart';
import 'package:signal_lab/data/model/general_setting/general_setting_response_model.dart';
import 'package:signal_lab/data/model/global/response_model/response_model.dart';
import 'package:signal_lab/data/repo/auth/login/login_repo.dart';
import 'package:signal_lab/views/components/signin_with_linkdin/signin_with_linkedin.dart';
import 'package:signal_lab/views/components/snackbar/show_custom_snackbar.dart';
import '../../../../../core/helper/shared_pref_helper.dart';
import '../../../../../core/route/route.dart';
import '../../../model/user/user.dart';
import 'package:google_sign_in/google_sign_in.dart';


class LoginController extends GetxController {
  LoginRepo loginRepo;
  LoginController({required this.loginRepo});

  final FocusNode emailFocusNode = FocusNode();
  final FocusNode passwordFocusNode = FocusNode();

  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();

  String? email;
  String? password;

  List<String> errors = [];
  bool remember = false;

  void forgetPassword() {
    Get.toNamed(RouteHelper.forgotPasswordScreen);
  }
  

  void checkAndGotoNextStep(LoginResponseModel responseModel) async {
    print('=== Starting checkAndGotoNextStep ===');

    User? user = responseModel.data?.user;
    print('User data: ${user?.toJson()}');

    bool needEmailVerification = user?.ev == "1" ? false : true;
    bool needSmsVerification = user?.sv == '1' ? false : true;
    bool isTwoFactorEnable = false;
    bool isProfileCompleteEnable = user?.profileComplete == '0' ? true : false;

    print('Email verification needed: $needEmailVerification (ev: ${user?.ev})');
    print('SMS verification needed: $needSmsVerification (sv: ${user?.sv})');
    print('Profile complete needed: $isProfileCompleteEnable (profileComplete: ${user?.profileComplete})');

    if (remember) {
      await loginRepo.apiClient.sharedPreferences.setBool(SharedPreferenceHelper.rememberMeKey, true);
    } else {
      await loginRepo.apiClient.sharedPreferences.setBool(SharedPreferenceHelper.rememberMeKey, false);
    }

    // Save user data to SharedPreferences with detailed logging
    String userId = user?.id.toString() ?? '-1';
    String accessToken = responseModel.data?.accessToken ?? '';
    String tokenType = responseModel.data?.tokenType ?? '';
    String userEmail = user?.email ?? '';
    String userPhone = user?.mobile ?? '';
    String userName = user?.username ?? '';

    print('=== SAVING USER DATA ===');
    print('User ID: $userId');
    print('Access Token: ${accessToken.isNotEmpty ? "${accessToken.substring(0, 10)}..." : "EMPTY"}');
    print('Token Type: $tokenType');
    print('User Email: $userEmail');
    print('User Phone: $userPhone');
    print('User Name: $userName');

    await loginRepo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.userIdKey, userId);
    await loginRepo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.accessTokenKey, accessToken);
    await loginRepo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.accessTokenType, tokenType);
    await loginRepo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.userEmailKey, userEmail);
    await loginRepo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.userPhoneNumberKey, userPhone);
    await loginRepo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.userNameKey, userName);

    // Verify the data was saved correctly
    String? savedToken = loginRepo.apiClient.sharedPreferences.getString(SharedPreferenceHelper.accessTokenKey);
    String? savedTokenType = loginRepo.apiClient.sharedPreferences.getString(SharedPreferenceHelper.accessTokenType);
    String? savedUserId = loginRepo.apiClient.sharedPreferences.getString(SharedPreferenceHelper.userIdKey);

    print('=== VERIFICATION ===');
    print('Saved Token: ${savedToken?.isNotEmpty == true ? "${savedToken!.substring(0, 10)}..." : "EMPTY"}');
    print('Saved Token Type: $savedTokenType');
    print('Saved User ID: $savedUserId');
    print('=== END SAVING USER DATA ===');

    // Reinitialize the API client token after saving
    loginRepo.apiClient.initToken();
    print('Reinitialized API client token');

    await loginRepo.sendUserToken();
    print('Sent user token');

    if (isProfileCompleteEnable) {
      print('Navigating to profile complete screen');
      Get.offAndToNamed(RouteHelper.profileCompleteScreen);
    } else if (needEmailVerification) {
      print('Navigating to email verification screen');
      Get.offAndToNamed(RouteHelper.emailVerificationScreen);
    } else if (needSmsVerification) {
      print('Navigating to SMS verification screen');
      Get.offAndToNamed(RouteHelper.smsVerificationScreen);
    } else {
      print('Navigating to home screen');
      Get.offAndToNamed(RouteHelper.homeScreen);
    }

    print('=== Finished checkAndGotoNextStep ===');
  }

  bool isSubmitLoading = false;
  void loginUser() async {
    print('=== LOGIN CONTROLLER: loginUser() method called ===');
    print('Email: ${emailController.text}');
    print('Password length: ${passwordController.text.length}');

    isSubmitLoading = true;
    update();

    ResponseModel model = await loginRepo.loginUser(emailController.text.toString(), passwordController.text.toString());

    print('Login API Response: ${model.responseJson}');
    print('Login API Status Code: ${model.statusCode}');

    if (model.statusCode == 200) {
      LoginResponseModel loginModel = LoginResponseModel.fromJson(jsonDecode(model.responseJson));
      print('Login Model Status: ${loginModel.status}');
      print('Login Model Data: ${loginModel.data?.toJson()}');

      if (loginModel.status.toString().toLowerCase() == MyStrings.success.toLowerCase()) {
        print('Login successful, proceeding to route user...');
        // Use the existing checkAndGotoNextStep method instead of RouteHelper
        checkAndGotoNextStep(loginModel);
        if (remember) {
          changeRememberMe();
        }
      } else {
        print('Login failed with message: ${loginModel.message?.error}');
        MySnackbar.error(errorList: loginModel.message?.error ?? [MyStrings.loginFailedTryAgain]);
      }
    } else {
      print('Login API failed with status: ${model.statusCode}, message: ${model.message}');
      MySnackbar.error(errorList: [model.message]);
    }

    isSubmitLoading = false;
    update();
  }

  changeRememberMe() {
    remember = !remember;
    update();
  }

  void clearTextField() {
    passwordController.text = '';
    emailController.text = '';

    if (remember) {
      remember = false;
    }
    update();
  }

  // final GoogleSignIn googleSignIn = GoogleSignIn();
  bool isGoogleSignInLoading = false;
  Future<void> signInWithGoogle() async {
    try {
      isGoogleSignInLoading = true;
      update();
      // googleSignIn.signOut();
      final GoogleSignInAccount? googleUser = null; //await googleSignIn.signIn();
      if (googleUser == null) {
        isGoogleSignInLoading = false;
        update();
        return;
      }
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // print(googleAuth.accessToken);
      await socialLoginUser(provider: 'google', accessToken: googleAuth.idToken ?? '');
      // await socialLoginUser(provider: 'google', accessToken: googleAuth.accessToken ?? ''); //old
    } catch (e) {
      debugPrint(e.toString());
      MySnackbar.error(errorList: [e.toString()]);
    }

    isGoogleSignInLoading = false;
    update();
  }

  //SIGN IN With LinkeDin
  bool isLinkedinLoading = false;
  Future<void> signInWithLinkedin(BuildContext context) async {
    try {
      isLinkedinLoading = false;
      update();

      SocialiteCredentials linkedinCredential = loginRepo.apiClient.getSocialCredentialsConfigData();
      String linkedinCredentialRedirectUrl = "${loginRepo.apiClient.getSocialCredentialsRedirectUrl()}/linkedin";
      print(linkedinCredentialRedirectUrl);
      print(linkedinCredential.linkedin?.toJson());
      SignInWithLinkedIn.signIn(
        context,
        config: LinkedInConfig(clientId: linkedinCredential.linkedin?.clientId ?? '', clientSecret: linkedinCredential.linkedin?.clientSecret ?? '', scope: ['openid', 'profile', 'email'], redirectUrl: "$linkedinCredentialRedirectUrl"),
        onGetAuthToken: (data) {
          print('Auth token data: ${data.toJson()}');
        },
        onGetUserProfile: (token, user) async {
          print('${token.idToken}-');
          print('LinkedIn User: ${user.toJson()}');

          await socialLoginUser(provider: 'linkedin', accessToken: token.accessToken ?? '');
        },
        onSignInError: (error) {
          print('Error on sign in: $error');
          MySnackbar.error(errorList: [error.description!] ?? [MyStrings.loginFailedTryAgain.tr]);
          isLinkedinLoading = false;
          update();
        },
      );
    } catch (e) {
      debugPrint(e.toString());

      MySnackbar.error(errorList: [e.toString()]);
    }
  }

  Future socialLoginUser({
    String accessToken = '',
    String? provider,
  }) async {
    try {
      ResponseModel responseModel = await loginRepo.socialLoginUser(
        accessToken: accessToken,
        provider: provider,
      );
      if (responseModel.statusCode == 200) {
        LoginResponseModel loginModel = LoginResponseModel.fromJson(jsonDecode(responseModel.responseJson));
        if (loginModel.status.toString().toLowerCase() == MyStrings.success.toLowerCase()) {
          remember = true;
          update();
          checkAndGotoNextStep(loginModel);
        } else {
          MySnackbar.error(errorList: loginModel.message?.error ?? [MyStrings.loginFailedTryAgain.tr]);
        }
      } else {
        MySnackbar.error(errorList: [responseModel.message]);
      }
    } catch (e) {
      //printx(e.toString());
    }
  }

  bool checkSocialAuthActiveOrNot({String provider = 'all'}) {
    if (provider == 'google') {
      return loginRepo.apiClient.getSocialCredentialsConfigData().google?.status == '1';
    } else if (provider == 'facebook') {
      return loginRepo.apiClient.getSocialCredentialsConfigData().facebook?.status == '1';
    } else if (provider == 'linkedin') {
      return loginRepo.apiClient.getSocialCredentialsConfigData().linkedin?.status == '1';
    } else {
      return loginRepo.apiClient.isSocialAnyOfSocialLoginOptionEnable();
    }
  }

}

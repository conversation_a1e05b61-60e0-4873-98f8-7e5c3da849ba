import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:signal_lab/core/utils/my_strings.dart';
import 'package:signal_lab/data/model/auth/login/login_response_model.dart';
import 'package:signal_lab/data/model/general_setting/general_setting_response_model.dart';
import 'package:signal_lab/data/model/global/response_model/response_model.dart';
import 'package:signal_lab/data/repo/auth/login/login_repo.dart';
import 'package:signal_lab/views/components/signin_with_linkdin/signin_with_linkedin.dart';
import 'package:signal_lab/views/components/snackbar/show_custom_snackbar.dart';
import '../../../../../core/helper/shared_pref_helper.dart';
import '../../../../../core/route/route.dart';
import '../../../model/user/user.dart';
import 'package:google_sign_in/google_sign_in.dart';


class LoginController extends GetxController {
  LoginRepo loginRepo;
  LoginController({required this.loginRepo});

  final FocusNode emailFocusNode = FocusNode();
  final FocusNode passwordFocusNode = FocusNode();

  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();

  String? email;
  String? password;

  List<String> errors = [];
  bool remember = false;

  void forgetPassword() {
    Get.toNamed(RouteHelper.forgotPasswordScreen);
  }
  

  void checkAndGotoNextStep(LoginResponseModel responseModel) async {
    User? user = responseModel.data?.user;

    bool needEmailVerification = user?.ev == "1" ? false : true;
    bool needSmsVerification = user?.sv == '1' ? false : true;
    bool isProfileCompleteEnable = user?.profileComplete == '0' ? true : false;

    if (remember) {
      await loginRepo.apiClient.sharedPreferences.setBool(SharedPreferenceHelper.rememberMeKey, true);
    } else {
      await loginRepo.apiClient.sharedPreferences.setBool(SharedPreferenceHelper.rememberMeKey, false);
    }

    // Save user data to SharedPreferences
    String userId = user?.id.toString() ?? '-1';
    String accessToken = responseModel.data?.accessToken ?? '';
    String tokenType = responseModel.data?.tokenType ?? '';
    String userEmail = user?.email ?? '';
    String userPhone = user?.mobile ?? '';
    String userName = user?.username ?? '';

    await loginRepo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.userIdKey, userId);
    await loginRepo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.accessTokenKey, accessToken);
    await loginRepo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.accessTokenType, tokenType);
    await loginRepo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.userEmailKey, userEmail);
    await loginRepo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.userPhoneNumberKey, userPhone);
    await loginRepo.apiClient.sharedPreferences.setString(SharedPreferenceHelper.userNameKey, userName);

    // Reinitialize the API client token after saving
    loginRepo.apiClient.initToken();

    await loginRepo.sendUserToken();

    if (isProfileCompleteEnable) {
      Get.offAndToNamed(RouteHelper.profileCompleteScreen);
    } else if (needEmailVerification) {
      Get.offAndToNamed(RouteHelper.emailVerificationScreen);
    } else if (needSmsVerification) {
      Get.offAndToNamed(RouteHelper.smsVerificationScreen);
    } else {
      Get.offAndToNamed(RouteHelper.homeScreen);
    }
  }

  bool isSubmitLoading = false;
  void loginUser() async {
    isSubmitLoading = true;
    update();

    ResponseModel model = await loginRepo.loginUser(emailController.text.toString(), passwordController.text.toString());

    if (model.statusCode == 200) {
      LoginResponseModel loginModel = LoginResponseModel.fromJson(jsonDecode(model.responseJson));

      if (loginModel.status.toString().toLowerCase() == MyStrings.success.toLowerCase()) {
        checkAndGotoNextStep(loginModel);
        if (remember) {
          changeRememberMe();
        }
      } else {
        MySnackbar.error(errorList: loginModel.message?.error ?? [MyStrings.loginFailedTryAgain]);
      }
    } else {
      MySnackbar.error(errorList: [model.message]);
    }

    isSubmitLoading = false;
    update();
  }

  void changeRememberMe() {
    remember = !remember;
    update();
  }

  void clearTextField() {
    passwordController.text = '';
    emailController.text = '';

    if (remember) {
      remember = false;
    }
    update();
  }

  final _googleSignIn = GoogleSignIn.instance;
  bool _isGoogleSignInInitialized = false;
  bool isGoogleSignInLoading = false;

  Future<void> _initializeGoogleSignIn() async {
    try {
      await _googleSignIn.initialize();
      _isGoogleSignInInitialized = true;
    } catch (e) {
      debugPrint('Failed to initialize Google Sign-In: $e');
    }
  }

  /// Always check Google sign in initialization before use
  Future<void> _ensureGoogleSignInInitialized() async {
    if (!_isGoogleSignInInitialized) {
      await _initializeGoogleSignIn();
    }
  }

  Future<void> signInWithGoogle() async {
    try {
      isGoogleSignInLoading = true;
      update();

      await _ensureGoogleSignInInitialized();

      try {
        // authenticate() throws exceptions instead of returning null
        final GoogleSignInAccount account = await _googleSignIn.authenticate(
          scopeHint: ['email'],  // Specify required scopes
        );
        final authClient = account.authorizationClient;
        final authorization = await authClient.authorizeScopes(['email']);

        debugPrint('Google Sign In successful - Access Token: ${authorization.accessToken}');
        // Use access token for authentication
        await socialLoginUser(provider: 'google', accessToken: authorization.accessToken);

      } on GoogleSignInException catch (e) {
        debugPrint('Google Sign In error: code: ${e.code.name} description:${e.description} details:${e.details}, error: $e');

        if (e.code == GoogleSignInExceptionCode.canceled) {
          MySnackbar.error(errorList: ['Google Sign In was canceled. Please try again.']);
        } else {
          MySnackbar.error(errorList: ['Google Sign In failed: ${e.description ?? 'Unknown error'}']);
        }
        rethrow;
      } catch (error) {
        debugPrint('Unexpected Google Sign-In error: $error');
        MySnackbar.error(errorList: ['Google Sign In failed: ${error.toString()}']);
        rethrow;
      }
    } catch (e) {
      debugPrint('Google Sign In outer catch: $e');
      // Don't show error again if already handled above
    }

    isGoogleSignInLoading = false;
    update();
  }

  //SIGN IN With LinkeDin
  bool isLinkedinLoading = false;
  Future<void> signInWithLinkedin(BuildContext context) async {
    try {
      isLinkedinLoading = false;
      update();

      SocialiteCredentials linkedinCredential = loginRepo.apiClient.getSocialCredentialsConfigData();
      String linkedinCredentialRedirectUrl = "${loginRepo.apiClient.getSocialCredentialsRedirectUrl()}/linkedin";
      SignInWithLinkedIn.signIn(
        context,
        config: LinkedInConfig(clientId: linkedinCredential.linkedin?.clientId ?? '', clientSecret: linkedinCredential.linkedin?.clientSecret ?? '', scope: ['openid', 'profile', 'email'], redirectUrl: linkedinCredentialRedirectUrl),
        onGetAuthToken: (data) {
          // Auth token received
        },
        onGetUserProfile: (token, user) async {
          await socialLoginUser(provider: 'linkedin', accessToken: token.accessToken ?? '');
        },
        onSignInError: (error) {
          MySnackbar.error(errorList: [error.description ?? MyStrings.loginFailedTryAgain.tr]);
          isLinkedinLoading = false;
          update();
        },
      );
    } catch (e) {
      debugPrint(e.toString());

      MySnackbar.error(errorList: [e.toString()]);
    }
  }

  Future socialLoginUser({
    String accessToken = '',
    String? provider,
  }) async {
    try {
      ResponseModel responseModel = await loginRepo.socialLoginUser(
        accessToken: accessToken,
        provider: provider,
      );
      if (responseModel.statusCode == 200) {
        LoginResponseModel loginModel = LoginResponseModel.fromJson(jsonDecode(responseModel.responseJson));
        if (loginModel.status.toString().toLowerCase() == MyStrings.success.toLowerCase()) {
          remember = true;
          update();
          checkAndGotoNextStep(loginModel);
        } else {
          MySnackbar.error(errorList: loginModel.message?.error ?? [MyStrings.loginFailedTryAgain.tr]);
        }
      } else {
        MySnackbar.error(errorList: [responseModel.message]);
      }
    } catch (e) {
      //printx(e.toString());
    }
  }

  bool checkSocialAuthActiveOrNot({String provider = 'all'}) {
    if (provider == 'google') {
      return loginRepo.apiClient.getSocialCredentialsConfigData().google?.status == '1';
    } else if (provider == 'facebook') {
      return loginRepo.apiClient.getSocialCredentialsConfigData().facebook?.status == '1';
    } else if (provider == 'linkedin') {
      return loginRepo.apiClient.getSocialCredentialsConfigData().linkedin?.status == '1';
    } else {
      return loginRepo.apiClient.isSocialAnyOfSocialLoginOptionEnable();
    }
  }

}
